import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Bell, Search, User, Check } from 'lucide-react';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { clearAllAlerts, markAlertAsRead } from '../../store/slices/alertSlice';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '../../contexts/NavigationContext';
import { getToken } from '../../utils/auth';

interface HeaderProps {
  onMenuToggle?: () => void;
  isSidebarOpen?: boolean;
}

const Header = ({ onMenuToggle, isSidebarOpen }: HeaderProps) => {
  const location = useLocation();
  const alerts = useAppSelector((state) => state.alerts.alerts);
  const dispatch = useAppDispatch();
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [persistentNotifications, setPersistentNotifications] = useState<any[]>([]);
  const { logout, user } = useAuth();
  const { currentSection } = useNavigation();

  // Calculer le nombre de notifications non lues (persistantes seulement)
  const unreadAlertsCount = persistentNotifications.filter(notif => !notif.read).length;

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/') return 'Dashboard';
    if (path.startsWith('/incidents')) {
      if (path === '/incidents') return 'Incidents';
      return 'Incident Details';
    }
    if (path === '/analytics') return 'Analytics';
    if (path === '/settings') return 'Settings';
    return '';
  };

  const handleMarkAllRead = async () => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch('http://localhost:5000/auth/notifications/mark-all-read', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Mettre à jour l'état local
        setPersistentNotifications(prev =>
          prev.map(notif => ({ ...notif, read: true }))
        );
        console.log('✅ All persistent notifications marked as read');
      }
    } catch (error) {
      console.error('❌ Failed to mark all notifications as read:', error);
    }
  };

  const handleMarkAsRead = (alertId: string) => {
    dispatch(markAlertAsRead(alertId));
    console.log(`✅ Notification ${alertId} marked as read`);
  };

  // Charger les notifications persistantes depuis la base de données
  const loadPersistentNotifications = async () => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch('http://localhost:5000/auth/notifications?limit=20', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPersistentNotifications(data.notifications || []);
        console.log('✅ Persistent notifications loaded:', data.notifications?.length || 0);
      }
    } catch (error) {
      console.error('❌ Failed to load persistent notifications:', error);
    }
  };

  // Marquer une notification persistante comme lue
  const markPersistentAsRead = async (notificationId: string) => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch(`http://localhost:5000/auth/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Mettre à jour l'état local
        setPersistentNotifications(prev =>
          prev.map(notif =>
            notif._id === notificationId
              ? { ...notif, read: true }
              : notif
          )
        );
        console.log(`✅ Persistent notification ${notificationId} marked as read`);
      }
    } catch (error) {
      console.error('❌ Failed to mark persistent notification as read:', error);
    }
  };

  // Charger les notifications au montage du composant et périodiquement
  useEffect(() => {
    if (user) {
      loadPersistentNotifications();

      // Recharger toutes les 10 secondes pour avoir les nouvelles notifications plus rapidement
      const interval = setInterval(() => {
        loadPersistentNotifications();
      }, 10000);

      return () => clearInterval(interval);
    }
  }, [user]);

  // Exposer la fonction de rechargement globalement pour les tests
  useEffect(() => {
    (window as any).reloadNotifications = loadPersistentNotifications;
    return () => {
      delete (window as any).reloadNotifications;
    };
  }, []);

  const handleLogout = () => {
    setShowProfileMenu(false);
    logout();
  };

  return (
    <header className="sticky top-0 z-10 h-16 bg-gray-900/95 backdrop-blur-xl border-b border-gray-700/50 shadow-lg">
      <div className="h-full px-6 flex items-center justify-between">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-white bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
            {currentSection}
          </h1>
        </div>

        <div className="hidden md:flex items-center flex-1 mx-6 max-w-md">
          <div className="w-full relative">
            <Search
              size={16}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search..."
              className="w-full h-10 pl-10 pr-4 rounded-xl bg-gray-800/50 border border-gray-600/50 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            />
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className={`relative p-3 rounded-xl text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-200 ${unreadAlertsCount > 0 ? 'pulse-animation' : ''}`}
            >
              <Bell size={20} />
              {unreadAlertsCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {unreadAlertsCount > 9 ? '9+' : unreadAlertsCount}
                </span>
              )}
            </button>

            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-gray-800/95 backdrop-blur-xl rounded-xl shadow-2xl border border-gray-600/50 animate-fadeIn z-50">
                <div className="p-4 border-b border-gray-600/50 flex items-center justify-between">
                  <h3 className="font-medium text-white">Notifications</h3>
                  {unreadAlertsCount > 0 && (
                    <button
                      onClick={handleMarkAllRead}
                      className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                    >
                      Mark all as read
                    </button>
                  )}
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {persistentNotifications.length > 0 ? (
                    persistentNotifications.slice(0, 10).map((notification) => (
                      <div
                        key={notification._id}
                        className={`p-4 border-b border-gray-600/30 hover:bg-gray-700/50 transition-colors ${
                          notification.read ? 'opacity-60 bg-gray-800/30' : ''
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div
                            className={`rounded-full h-3 w-3 mt-1 ${
                              notification.type === 'security_alerts'
                                ? 'bg-red-500'
                                : notification.type === 'login_alerts'
                                  ? 'bg-yellow-500'
                                  : notification.type === 'analysis_completed'
                                    ? 'bg-green-500'
                                    : 'bg-blue-500'
                            }`}
                          />
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <p className={`font-medium text-sm ${
                                  notification.read ? 'text-gray-400' : 'text-white'
                                }`}>
                                  {notification.title}
                                </p>
                                <p className={`text-xs mt-1 ${
                                  notification.read ? 'text-gray-500' : 'text-gray-300'
                                }`}>
                                  {notification.message}
                                </p>
                                <p className="text-xs text-gray-400 mt-2">
                                  {new Date(notification.created_at).toLocaleTimeString()}
                                </p>
                              </div>
                              {!notification.read && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    markPersistentAsRead(notification._id);
                                  }}
                                  className="ml-2 p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded-full transition-colors"
                                  title="Mark as read"
                                >
                                  <Check className="w-3 h-3" />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-6 text-center text-gray-400">No notifications</div>
                  )}
                </div>
                <div className="p-3 text-center border-t border-gray-600/50">
                  <button className="text-xs text-purple-400 hover:text-purple-300 transition-colors">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="relative">
            <button
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-800/50 transition-all duration-200"
            >
              {user?.avatar ? (
                <img
                  src={`http://localhost:5000${user.avatar}`}
                  alt="User Avatar"
                  className="h-9 w-9 rounded-xl object-cover shadow-lg border-2 border-purple-500/30"
                />
              ) : (
                <div className="h-9 w-9 rounded-xl bg-gradient-to-br from-purple-500 to-cyan-500 flex items-center justify-center text-white shadow-lg">
                  {user?.first_name && user?.last_name
                    ? `${user.first_name[0]}${user.last_name[0]}`
                    : <User size={18} />
                  }
                </div>
              )}
            </button>

            {showProfileMenu && (
              <div className="absolute right-0 mt-2 w-56 bg-gray-800/95 backdrop-blur-xl rounded-xl shadow-2xl border border-gray-600/50 animate-fadeIn z-50">
                <div className="p-4 border-b border-gray-600/50">
                  <p className="font-medium text-white">{user?.username || 'User'}</p>
                  <p className="text-sm text-gray-300 mt-1">{user?.email}</p>
                  <p className="text-xs text-gray-400 mt-2 capitalize bg-purple-600/20 text-purple-300 px-2 py-1 rounded-md inline-block">
                    {user?.role} role
                  </p>
                </div>
                <div className="p-2">
                  <button
                    onClick={handleLogout}
                    className="w-full text-left px-3 py-2 text-sm hover:bg-red-600/20 rounded-lg text-red-400 hover:text-red-300 transition-all duration-200"
                  >
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
