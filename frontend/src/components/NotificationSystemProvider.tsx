import { ReactNode, createContext, useContext } from 'react';
import { useNotificationSystem } from '../hooks/useNotificationSystem';

interface NotificationSystemProviderProps {
  children: ReactNode;
}

interface NotificationSystemContextType {
  reloadNotifications: (() => void) | null;
  setReloadNotifications: (fn: () => void) => void;
}

const NotificationSystemContext = createContext<NotificationSystemContextType>({
  reloadNotifications: null,
  setReloadNotifications: () => {},
});

export const useNotificationSystemContext = () => useContext(NotificationSystemContext);

/**
 * Composant pour initialiser le système de notifications
 * Doit être placé à l'intérieur des providers NotificationContext et Redux
 */
export const NotificationSystemProvider = ({ children }: NotificationSystemProviderProps) => {
  let reloadNotifications: (() => void) | null = null;

  const setReloadNotifications = (fn: () => void) => {
    reloadNotifications = fn;
  };

  // Initialiser le système de notifications
  useNotificationSystem(reloadNotifications || undefined);

  return (
    <NotificationSystemContext.Provider value={{ reloadNotifications, setReloadNotifications }}>
      {children}
    </NotificationSystemContext.Provider>
  );
};

export default NotificationSystemProvider;
