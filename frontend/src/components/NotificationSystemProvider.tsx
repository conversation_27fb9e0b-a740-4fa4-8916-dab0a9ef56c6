import { ReactNode } from 'react';
import { useNotificationSystem } from '../hooks/useNotificationSystem';

interface NotificationSystemProviderProps {
  children: ReactNode;
}

/**
 * Composant pour initialiser le système de notifications
 * Doit être placé à l'intérieur des providers NotificationContext et Redux
 */
export const NotificationSystemProvider = ({ children }: NotificationSystemProviderProps) => {
  // Initialiser le système de notifications
  useNotificationSystem();
  
  return <>{children}</>;
};

export default NotificationSystemProvider;
