import { getToken } from '../utils/auth';

export interface NotificationPreferences {
  security_alerts: boolean;
  system_updates: boolean;
  analysis_completed: boolean;
  login_alerts: boolean;
  profile_changes: boolean;
  email_notifications: boolean;
  browser_notifications: boolean;
}

export interface NotificationData {
  type: 'security_alerts' | 'system_updates' | 'analysis_completed' | 'login_alerts' | 'profile_changes';
  title: string;
  message: string;
  icon?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

class NotificationService {
  private preferences: NotificationPreferences | null = null;
  private browserPermission: NotificationPermission = 'default';

  constructor() {
    this.initializeBrowserNotifications();
    this.loadPreferences();
  }

  /**
   * Initialise les notifications browser
   */
  private async initializeBrowserNotifications() {
    if ('Notification' in window) {
      this.browserPermission = Notification.permission;
      
      // Si les permissions ne sont pas encore accordées, on les demandera plus tard
      if (this.browserPermission === 'default') {
        console.log('🔔 Browser notifications permission not yet requested');
      }
    } else {
      console.warn('⚠️ Browser notifications not supported');
    }
  }

  /**
   * Demande la permission pour les notifications browser
   */
  async requestBrowserPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('⚠️ Browser notifications not supported');
      return false;
    }

    if (this.browserPermission === 'granted') {
      return true;
    }

    try {
      const permission = await Notification.requestPermission();
      this.browserPermission = permission;
      
      if (permission === 'granted') {
        console.log('✅ Browser notifications permission granted');
        return true;
      } else {
        console.log('❌ Browser notifications permission denied');
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting notification permission:', error);
      return false;
    }
  }

  /**
   * Charge les préférences utilisateur depuis l'API
   */
  private async loadPreferences() {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch('http://localhost:5000/auth/notification-preferences', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        this.preferences = await response.json();
        console.log('✅ Notification preferences loaded:', this.preferences);
      }
    } catch (error) {
      console.error('❌ Failed to load notification preferences:', error);
    }
  }

  /**
   * Met à jour les préférences en cache
   */
  updatePreferences(newPreferences: NotificationPreferences) {
    this.preferences = newPreferences;
    console.log('🔄 Notification preferences updated:', this.preferences);
  }

  /**
   * Vérifie si un type de notification est activé
   */
  private isNotificationEnabled(type: NotificationData['type']): boolean {
    if (!this.preferences) {
      console.warn('⚠️ Notification preferences not loaded yet');
      return false;
    }

    return this.preferences[type] === true;
  }

  /**
   * Vérifie si les notifications browser sont activées
   */
  private isBrowserNotificationEnabled(): boolean {
    if (!this.preferences) return false;
    return this.preferences.browser_notifications === true;
  }

  /**
   * Envoie une notification browser native
   */
  private async sendBrowserNotification(data: NotificationData) {
    if (!this.isBrowserNotificationEnabled()) {
      console.log('🔕 Browser notifications disabled by user');
      return;
    }

    if (this.browserPermission !== 'granted') {
      console.log('🔔 Requesting browser notification permission...');
      const granted = await this.requestBrowserPermission();
      if (!granted) return;
    }

    try {
      const notification = new Notification(data.title, {
        body: data.message,
        icon: data.icon || '/favicon.ico',
        badge: '/favicon.ico',
        tag: data.type, // Évite les doublons
        requireInteraction: data.type === 'security_alerts', // Les alertes sécurité restent visibles
        data: data.data,
        actions: data.actions,
      });

      // Auto-fermeture après 8 secondes (sauf pour les alertes sécurité)
      if (data.type !== 'security_alerts') {
        setTimeout(() => {
          notification.close();
        }, 8000);
      }

      // Gestion des clics
      notification.onclick = () => {
        window.focus();
        notification.close();
        
        // Actions spécifiques selon le type
        this.handleNotificationClick(data);
      };

      console.log(`🔔 Browser notification sent: ${data.title}`);
    } catch (error) {
      console.error('❌ Failed to send browser notification:', error);
    }
  }

  /**
   * Gère les clics sur les notifications
   */
  private handleNotificationClick(data: NotificationData) {
    switch (data.type) {
      case 'analysis_completed':
        // Rediriger vers la page Analytics
        window.location.href = '/analytics';
        break;
      case 'security_alerts':
      case 'login_alerts':
        // Rediriger vers les paramètres de sécurité
        window.location.href = '/settings?tab=security';
        break;
      case 'profile_changes':
        // Rediriger vers les paramètres de profil
        window.location.href = '/settings?tab=profile';
        break;
      case 'system_updates':
        // Rediriger vers le dashboard
        window.location.href = '/';
        break;
    }
  }

  /**
   * Envoie une notification (point d'entrée principal)
   */
  async sendNotification(data: NotificationData) {
    console.log(`📢 Attempting to send notification: ${data.type} - ${data.title}`);

    // Vérifier si ce type de notification est activé
    if (!this.isNotificationEnabled(data.type)) {
      console.log(`🔕 Notification type '${data.type}' is disabled by user`);
      return;
    }

    // Envoyer la notification browser si activée
    if (this.isBrowserNotificationEnabled()) {
      await this.sendBrowserNotification(data);
    }

    // Ici on pourrait aussi envoyer vers d'autres systèmes :
    // - Toast notifications
    // - Header notification bell
    // - Email (si activé)
    // - etc.
  }

  /**
   * Méthodes de convenance pour chaque type de notification
   */
  async sendSecurityAlert(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'security_alerts',
      title,
      message,
      icon: '🛡️',
      data,
    });
  }

  async sendSystemUpdate(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'system_updates',
      title,
      message,
      icon: '🔄',
      data,
    });
  }

  async sendAnalysisCompleted(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'analysis_completed',
      title,
      message,
      icon: '📊',
      data,
    });
  }

  async sendLoginAlert(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'login_alerts',
      title,
      message,
      icon: '🔑',
      data,
    });
  }

  async sendProfileChange(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'profile_changes',
      title,
      message,
      icon: '👤',
      data,
    });
  }
}

// Instance singleton
export const notificationService = new NotificationService();
export default notificationService;
