import { getToken } from '../utils/auth';

export interface NotificationPreferences {
  security_alerts: boolean;
  system_updates: boolean;
  analysis_completed: boolean;
  login_alerts: boolean;
  profile_changes: boolean;
  email_notifications: boolean;
  toast_notifications: boolean; // Remplace browser_notifications
}

export interface NotificationData {
  type: 'security_alerts' | 'system_updates' | 'analysis_completed' | 'login_alerts' | 'profile_changes';
  title: string;
  message: string;
  icon?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

// Interfaces pour les callbacks des systèmes de notifications
interface ToastCallbacks {
  showSuccess: (message: string, title?: string) => void;
  showError: (message: string, title?: string) => void;
  showWarning: (message: string, title?: string) => void;
  showInfo: (message: string, title?: string) => void;
}

interface HeaderNotificationCallbacks {
  addNotification: (notification: any) => void;
  reloadNotifications?: () => void;
}

class NotificationService {
  private preferences: NotificationPreferences | null = null;
  private toastCallbacks: ToastCallbacks | null = null;
  private headerCallbacks: HeaderNotificationCallbacks | null = null;

  constructor() {
    this.loadPreferences();
  }

  /**
   * Enregistre les callbacks pour les toasts
   */
  setToastCallbacks(callbacks: ToastCallbacks) {
    this.toastCallbacks = callbacks;
    console.log('✅ Toast callbacks registered');
  }

  /**
   * Enregistre les callbacks pour les notifications header
   */
  setHeaderCallbacks(callbacks: HeaderNotificationCallbacks) {
    this.headerCallbacks = callbacks;
    console.log('✅ Header notification callbacks registered');
  }

  /**
   * Charge les préférences utilisateur depuis l'API
   */
  private async loadPreferences() {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch('http://localhost:5000/auth/notification-preferences', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        this.preferences = await response.json();
        console.log('✅ Notification preferences loaded:', this.preferences);
      }
    } catch (error) {
      console.error('❌ Failed to load notification preferences:', error);
    }
  }

  /**
   * Met à jour les préférences en cache
   */
  updatePreferences(newPreferences: NotificationPreferences) {
    this.preferences = newPreferences;
    console.log('🔄 Notification preferences updated:', this.preferences);
  }

  /**
   * Vérifie si un type de notification est activé
   */
  private isNotificationEnabled(type: NotificationData['type']): boolean {
    if (!this.preferences) {
      console.warn('⚠️ Notification preferences not loaded yet');
      return false;
    }

    return this.preferences[type] === true;
  }

  /**
   * Vérifie si les notifications toast sont activées
   */
  private isToastNotificationEnabled(): boolean {
    if (!this.preferences) return false;
    return this.preferences.toast_notifications === true;
  }

  /**
   * Envoie une notification toast
   */
  private sendToastNotification(data: NotificationData) {
    if (!this.isToastNotificationEnabled()) {
      console.log('🔕 Toast notifications disabled by user');
      return;
    }

    if (!this.toastCallbacks) {
      console.warn('⚠️ Toast callbacks not registered');
      return;
    }

    try {
      // Choisir le type de toast selon le type de notification
      switch (data.type) {
        case 'security_alerts':
          this.toastCallbacks.showError(data.message, data.title);
          break;
        case 'system_updates':
          this.toastCallbacks.showInfo(data.message, data.title);
          break;
        case 'analysis_completed':
          this.toastCallbacks.showSuccess(data.message, data.title);
          break;
        case 'login_alerts':
          this.toastCallbacks.showWarning(data.message, data.title);
          break;
        case 'profile_changes':
          this.toastCallbacks.showSuccess(data.message, data.title);
          break;
        default:
          this.toastCallbacks.showInfo(data.message, data.title);
      }

      console.log(`🍞 Toast notification sent: ${data.title}`);
    } catch (error) {
      console.error('❌ Failed to send toast notification:', error);
    }
  }

  /**
   * Envoie une notification dans le header (cloche)
   */
  private sendHeaderNotification(data: NotificationData) {
    if (!this.headerCallbacks) {
      console.warn('⚠️ Header notification callbacks not registered');
      return;
    }

    try {
      const headerNotification = {
        id: Date.now().toString(),
        type: data.type,
        title: data.title,
        message: data.message,
        timestamp: new Date().toISOString(),
        read: false,
        data: data.data,
      };

      this.headerCallbacks.addNotification(headerNotification);
      console.log(`🔔 Header notification sent: ${data.title}`);
    } catch (error) {
      console.error('❌ Failed to send header notification:', error);
    }
  }

  /**
   * Gère les clics sur les notifications
   */
  private handleNotificationClick(data: NotificationData) {
    switch (data.type) {
      case 'analysis_completed':
        // Rediriger vers la page Analytics
        window.location.href = '/analytics';
        break;
      case 'security_alerts':
      case 'login_alerts':
        // Rediriger vers les paramètres de sécurité
        window.location.href = '/settings?tab=security';
        break;
      case 'profile_changes':
        // Rediriger vers les paramètres de profil
        window.location.href = '/settings?tab=profile';
        break;
      case 'system_updates':
        // Rediriger vers le dashboard
        window.location.href = '/';
        break;
    }
  }

  /**
   * Envoie une notification (point d'entrée principal)
   */
  async sendNotification(data: NotificationData) {
    console.log(`📢 Attempting to send notification: ${data.type} - ${data.title}`);

    // Vérifier si ce type de notification est activé
    if (!this.isNotificationEnabled(data.type)) {
      console.log(`🔕 Notification type '${data.type}' is disabled by user`);
      return;
    }

    // Envoyer la notification toast (pour les actions immédiates)
    this.sendToastNotification(data);

    // Envoyer la notification dans le header (pour la persistance)
    // SÉPARÉMENT du toast - elles ne sont pas liées
    this.sendHeaderNotification(data);

    // Sauvegarder en base de données pour la persistance
    await this.saveNotificationToDatabase(data);

    // Ici on pourrait aussi envoyer vers d'autres systèmes :
    // - Email (si activé)
    // - WebSocket pour temps réel
    // - etc.
  }

  /**
   * Sauvegarde la notification en base de données
   */
  private async saveNotificationToDatabase(data: NotificationData) {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch('http://localhost:5000/auth/notifications', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: data.type,
          title: data.title,
          message: data.message,
          data: data.data,
        }),
      });

      if (response.ok) {
        console.log('✅ Notification saved to database');

        // Recharger les notifications dans le header
        if (this.headerCallbacks?.reloadNotifications) {
          this.headerCallbacks.reloadNotifications();
        }
      }
    } catch (error) {
      console.error('❌ Failed to save notification to database:', error);
    }
  }

  /**
   * Méthodes de convenance pour chaque type de notification
   */
  async sendSecurityAlert(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'security_alerts',
      title,
      message,
      icon: '🛡️',
      data,
    });
  }

  async sendSystemUpdate(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'system_updates',
      title,
      message,
      icon: '🔄',
      data,
    });
  }

  async sendAnalysisCompleted(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'analysis_completed',
      title,
      message,
      icon: '📊',
      data,
    });
  }

  async sendLoginAlert(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'login_alerts',
      title,
      message,
      icon: '🔑',
      data,
    });
  }

  async sendProfileChange(title: string, message: string, data?: any) {
    await this.sendNotification({
      type: 'profile_changes',
      title,
      message,
      icon: '👤',
      data,
    });
  }
}

// Instance singleton
export const notificationService = new NotificationService();
export default notificationService;
