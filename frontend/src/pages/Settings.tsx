import { useState, useEffect } from 'react';
import {
  User,
  <PERSON>,
  Shield,
  Users,
  Zap,
  Key,
  Camera,
  Save,
  Mail,
  MessageSquare,
  Monitor,
  Activity,
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { useNotifications } from '../hooks/useNotifications';
import { useNotifications as useToasts } from '../contexts/NotificationContext';
import { getToken } from '../utils/auth';

interface NotificationSettings {
  // Incident notifications (admin only)
  email_enabled: boolean;
  email_address: string;
  telegram_enabled: boolean;
  telegram_chat_id: string;

  // User notifications (all users)
  security_alerts: boolean;
  system_updates: boolean;
  analysis_completed: boolean;
  login_alerts: boolean;
  profile_changes: boolean;
  email_notifications: boolean;
  toast_notifications: boolean; // Remplace browser_notifications
}

interface SecuritySettings {
  two_factor_enabled: boolean;
  password_last_changed: string | null;
  login_sessions: Array<{
    id: string;
    device: string;
    location: string;
    ip_address: string;
    last_active: string;
    is_current: boolean;
  }>;
  security_questions: Array<{
    question: string;
    answer_set: boolean;
  }>;
  account_lockout: {
    enabled: boolean;
    max_attempts: number;
    lockout_duration: number;
  };
  password_policy: {
    min_length: number;
    require_uppercase: boolean;
    require_lowercase: boolean;
    require_numbers: boolean;
    require_symbols: boolean;
    password_expiry_days: number;
  };
}

export default function Settings() {
  const { user, updateUser } = useAuth();
  const { isAdmin } = useRole();
  const {
    updatePreferences,
    sendProfileChange
  } = useNotifications();
  const { showSuccess, showError } = useToasts();
  const [activeTab, setActiveTab] = useState('profile');

  // Debug: log user data
  useEffect(() => {
    console.log('Current user from useAuth:', user);
    console.log('Is admin:', isAdmin);
  }, [user, isAdmin]);

  // Current user data (read-only display)
  const [currentFirstName, setCurrentFirstName] = useState('');
  const [currentLastName, setCurrentLastName] = useState('');
  const [currentUsername, setCurrentUsername] = useState('');
  const [currentEmail, setCurrentEmail] = useState('');
  const [currentRole, setCurrentRole] = useState('');
  const [currentAvatar, setCurrentAvatar] = useState('');

  // Form states (for new values)
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('');
  const [avatar, setAvatar] = useState('');

  // Loading and error states
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [profileSuccess, setProfileSuccess] = useState<string | null>(null);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string }>({});
  const [dataLoading, setDataLoading] = useState(true);

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    // Incident notifications (admin only)
    email_enabled: false,
    email_address: '',
    telegram_enabled: false,
    telegram_chat_id: '',

    // User notifications (all users)
    security_alerts: true,
    system_updates: true,
    analysis_completed: true,
    login_alerts: false,
    profile_changes: true,
    email_notifications: true,
    toast_notifications: true,
  });
  const [notificationLoading, setNotificationLoading] = useState(false);
  const [notificationSuccess, setNotificationSuccess] = useState<string | null>(null);
  const [notificationError, setNotificationError] = useState<string | null>(null);

  // Security settings
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    two_factor_enabled: false,
    password_last_changed: null,
    login_sessions: [],
    security_questions: [],
    account_lockout: {
      enabled: true,
      max_attempts: 5,
      lockout_duration: 30,
    },
    password_policy: {
      min_length: 8,
      require_uppercase: true,
      require_lowercase: true,
      require_numbers: true,
      require_symbols: false,
      password_expiry_days: 90,
    },
  });
  const [securityLoading, setSecurityLoading] = useState(false);
  const [securitySuccess, setSecuritySuccess] = useState<string | null>(null);
  const [securityError, setSecurityError] = useState<string | null>(null);

  // Password change form
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'team', label: 'Team', icon: Users },
    { id: 'integrations', label: 'Integrations', icon: Zap },
    { id: 'api', label: 'API Access', icon: Key },
  ];

  useEffect(() => {
    if (activeTab === 'notifications') {
      loadNotificationSettings();
    } else if (activeTab === 'security') {
      loadSecuritySettings();
    }
  }, [activeTab]);

  useEffect(() => {
    loadUserProfile();
  }, [user]);

  const loadUserProfile = async () => {
    setDataLoading(true);
    try {
      // First, try to use data from auth context (most reliable)
      if (user) {
        console.log('Loading user data from context:', user);
        setCurrentFirstName(user.first_name || user.firstName || '');
        setCurrentLastName(user.last_name || user.lastName || '');
        setCurrentUsername(user.username || '');
        setCurrentEmail(user.email || '');
        setCurrentRole(user.role || '');
        setCurrentAvatar(user.avatar || '');

        // Keep form fields empty for new values
        setFirstName('');
        setLastName('');
        setUsername('');
        setEmail('');
        setRole(user.role || '');
        setAvatar(user.avatar || '');

        setDataLoading(false);
        return;
      }

      // Fallback to API call if context data is not available or incomplete
      const token = getToken();
      if (!token) {
        console.error('No authentication token found');
        setDataLoading(false);
        return;
      }

      console.log('Loading user data from API...');
      const response = await fetch('http://localhost:5000/auth/profile', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('User data from API:', userData);
        console.log('Setting profile data:', {
          firstName: userData.first_name,
          lastName: userData.last_name,
          username: userData.username,
          email: userData.email,
          role: userData.role,
          avatar: userData.avatar
        });

        // Set current user data (for display)
        setCurrentFirstName(userData.first_name || '');
        setCurrentLastName(userData.last_name || '');
        setCurrentUsername(userData.username || '');
        setCurrentEmail(userData.email || '');
        setCurrentRole(userData.role || '');
        setCurrentAvatar(userData.avatar || '');

        // Keep form fields empty for new values
        setFirstName('');
        setLastName('');
        setUsername('');
        setEmail('');
        setRole(userData.role || '');
        setAvatar(userData.avatar || '');

        // Update the auth context with complete user data
        updateUser(userData);
      } else {
        console.error('Failed to load user profile from API:', response.status, response.statusText);
        const errorData = await response.text();
        console.error('API Error details:', errorData);
      }
    } catch (err) {
      console.error('Failed to load user profile:', err);
    } finally {
      setDataLoading(false);
    }
  };

  const loadNotificationSettings = async () => {
    try {
      const token = getToken();
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      // Charger les préférences utilisateur
      const userResponse = await fetch('http://localhost:5000/auth/notification-preferences', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (userResponse.ok) {
        const userPreferences = await userResponse.json();
        console.log('✅ User notification preferences loaded:', userPreferences);

        setNotificationSettings(prev => ({
          ...prev,
          ...userPreferences
        }));
      }

      // Charger les paramètres d'incidents (admin seulement)
      if (isAdmin) {
        const incidentResponse = await fetch('http://localhost:5000/api/incident/notification-settings', {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (incidentResponse.ok) {
          const incidentSettings = await incidentResponse.json();
          console.log('✅ Admin incident settings loaded:', incidentSettings);

          setNotificationSettings(prev => ({
            ...prev,
            // Update incident notifications (admin only)
            email_enabled: incidentSettings.email_enabled || false,
            email_address: incidentSettings.email_address || '',
            telegram_enabled: incidentSettings.telegram_enabled || false,
            telegram_chat_id: incidentSettings.telegram_chat_id || '',
          }));
        }
      }
    } catch (err) {
      console.error('Failed to load notification settings:', err);
    }
  };

  const loadSecuritySettings = async () => {
    try {
      setSecurityLoading(true);
      setSecurityError(null);

      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/auth/security-settings', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('🔒 Loaded security settings:', data);
        setSecuritySettings(data);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load security settings');
      }
    } catch (error) {
      console.error('❌ Error loading security settings:', error);
      setSecurityError(error instanceof Error ? error.message : 'Failed to load security settings');
    } finally {
      setSecurityLoading(false);
    }
  };

  const saveNotificationSettings = async () => {
    setNotificationLoading(true);
    setNotificationError(null);
    setNotificationSuccess(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Sauvegarder les préférences utilisateur
      const userPreferences = {
        security_alerts: notificationSettings.security_alerts,
        system_updates: notificationSettings.system_updates,
        analysis_completed: notificationSettings.analysis_completed,
        login_alerts: notificationSettings.login_alerts,
        profile_changes: notificationSettings.profile_changes,
        email_notifications: notificationSettings.email_notifications,
        toast_notifications: notificationSettings.toast_notifications,
      };

      const userResponse = await fetch('http://localhost:5000/auth/notification-preferences', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userPreferences),
      });

      if (!userResponse.ok) {
        throw new Error('Failed to save user notification preferences');
      }

      // Sauvegarder les paramètres d'incidents (admin seulement)
      if (isAdmin) {
        const incidentSettings = {
          email_enabled: notificationSettings.email_enabled,
          email_address: notificationSettings.email_address,
          telegram_enabled: notificationSettings.telegram_enabled,
          telegram_chat_id: notificationSettings.telegram_chat_id,
        };

        const incidentResponse = await fetch('http://localhost:5000/api/incident/notification-settings', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(incidentSettings),
        });

        if (!incidentResponse.ok) {
          throw new Error('Failed to save incident notification settings');
        }
      }

      // Mettre à jour le service de notifications avec les nouvelles préférences
      updatePreferences(userPreferences);

      // Afficher un toast de succès
      showSuccess('Notification preferences saved successfully!', '✅ Settings Saved');

      setNotificationSuccess('✅ Notification preferences saved successfully!');
      setTimeout(() => setNotificationSuccess(null), 4000);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save settings';

      // Afficher un toast d'erreur
      showError(errorMessage, '❌ Save Failed');

      setNotificationError(errorMessage);
    } finally {
      setNotificationLoading(false);
    }
  };

  const changePassword = async () => {
    try {
      setSecurityLoading(true);
      setSecurityError(null);
      setSecuritySuccess(null);

      if (!currentPassword || !newPassword || !confirmPassword) {
        throw new Error('All password fields are required');
      }

      if (newPassword !== confirmPassword) {
        throw new Error('New passwords do not match');
      }

      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/auth/change-password', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword,
        }),
      });

      if (response.ok) {
        setSecuritySuccess('Password changed successfully');
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');

        // Recharger les paramètres de sécurité
        await loadSecuritySettings();

        // Afficher un toast de succès
        showSuccess('Password changed successfully');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to change password');
      }
    } catch (error) {
      console.error('❌ Error changing password:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
      setSecurityError(errorMessage);
      showError(errorMessage);
    } finally {
      setSecurityLoading(false);
    }
  };

  const terminateSession = async (sessionId: string) => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`http://localhost:5000/auth/terminate-session/${sessionId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        showSuccess('Session terminated successfully');
        await loadSecuritySettings(); // Recharger les sessions
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to terminate session');
      }
    } catch (error) {
      console.error('❌ Error terminating session:', error);
      showError(error instanceof Error ? error.message : 'Failed to terminate session');
    }
  };

  const terminateAllSessions = async () => {
    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/auth/terminate-all-sessions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        showSuccess(data.message);
        await loadSecuritySettings(); // Recharger les sessions
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to terminate sessions');
      }
    } catch (error) {
      console.error('❌ Error terminating all sessions:', error);
      showError(error instanceof Error ? error.message : 'Failed to terminate sessions');
    }
  };

  const validateForm = (updateData: any) => {
    const errors: { [key: string]: string } = {};

    if ('first_name' in updateData) {
      if (!updateData.first_name.trim()) {
        errors.firstName = 'First name cannot be empty';
      } else if (updateData.first_name.trim().length < 2) {
        errors.firstName = 'First name must be at least 2 characters';
      }
    }

    if ('last_name' in updateData) {
      if (!updateData.last_name.trim()) {
        errors.lastName = 'Last name cannot be empty';
      } else if (updateData.last_name.trim().length < 2) {
        errors.lastName = 'Last name must be at least 2 characters';
      }
    }

    if ('username' in updateData) {
      if (!updateData.username.trim()) {
        errors.username = 'Username cannot be empty';
      } else if (updateData.username.trim().length < 3) {
        errors.username = 'Username must be at least 3 characters';
      }
    }

    if ('email' in updateData) {
      if (!updateData.email.trim()) {
        errors.email = 'Email cannot be empty';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updateData.email)) {
        errors.email = 'Please enter a valid email address';
      }
    }

    return errors;
  };

  const handleSaveChanges = async () => {
    setProfileLoading(true);
    setProfileError(null);
    setProfileSuccess(null);

    // Only include fields that have been modified (non-empty)
    const updateData: any = {};

    if (firstName.trim()) {
      updateData.first_name = firstName.trim();
    }
    if (lastName.trim()) {
      updateData.last_name = lastName.trim();
    }
    if (username.trim()) {
      updateData.username = username.trim();
    }
    if (email.trim()) {
      updateData.email = email.trim();
    }

    // Check if there are any changes to save
    if (Object.keys(updateData).length === 0) {
      setProfileError('Please enter at least one field to update (First Name, Last Name, Username, or Email)');
      setProfileLoading(false);
      return;
    }

    // Validate form
    const validationErrors = validateForm(updateData);
    if (Object.keys(validationErrors).length > 0) {
      setFieldErrors(validationErrors);
      setProfileError('Please fix the errors above');
      setProfileLoading(false);
      return;
    }

    // Clear field errors if validation passes
    setFieldErrors({});

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/auth/profile', {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const result = await response.json();
        setProfileSuccess('Profile updated successfully!');

        // Afficher un toast de succès
        showSuccess('Your profile has been updated successfully!', '✅ Profile Updated');

        // Envoyer une notification de changement de profil
        await sendProfileChange(
          '👤 Profile Updated',
          'Your profile information has been successfully updated'
        );

        // Update user data in AuthContext and local state
        if (result.user) {
          // Update AuthContext with new user data
          updateUser(result.user);

          // Update local display state
          setCurrentFirstName(result.user.first_name || '');
          setCurrentLastName(result.user.last_name || '');
          setCurrentUsername(result.user.username || '');
          setCurrentEmail(result.user.email || '');
          setCurrentRole(result.user.role || '');
        }

        // Clear form fields after successful update
        setFirstName('');
        setLastName('');
        setUsername('');
        setEmail('');

        // Clear success message after 3 seconds
        setTimeout(() => {
          setProfileSuccess(null);
        }, 3000);
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update profile');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';

      // Afficher un toast d'erreur
      showError(errorMessage, '❌ Profile Update Failed');

      setProfileError(errorMessage);
    } finally {
      setProfileLoading(false);
    }
  };

  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setAvatarLoading(true);
    setProfileError(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const formData = new FormData();
      formData.append('avatar', file);

      const response = await fetch('http://localhost:5000/auth/avatar', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();

        // Update AuthContext with new avatar
        console.log('Updating user avatar in context:', result.avatar_url);
        updateUser({ avatar: result.avatar_url });

        // Update local state
        setCurrentAvatar(result.avatar_url);
        setAvatar(result.avatar_url);
        setProfileSuccess('Avatar updated and saved successfully! ✨');
        setTimeout(() => setProfileSuccess(null), 4000);
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload avatar');
      }
    } catch (err) {
      setProfileError(err instanceof Error ? err.message : 'Failed to upload avatar');
    } finally {
      setAvatarLoading(false);
    }
  };

  const renderProfileTab = () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Profile Settings</h2>

        {/* Loading indicator */}
        {dataLoading && (
          <div className="bg-gray-900/60 border border-gray-700/50 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-400"></div>
              <p className="text-gray-300">Loading profile data...</p>
            </div>
          </div>
        )}

        {/* Success/Error Messages */}
        {profileSuccess && (
          <div className="bg-green-900/60 border border-green-700/50 rounded-xl p-4 mb-6 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-green-900" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <p className="text-green-300 font-medium">{profileSuccess}</p>
            </div>
          </div>
        )}
        {profileError && (
          <div className="bg-red-900/60 border border-red-700/50 rounded-xl p-4 mb-6">
            <p className="text-red-300">{profileError}</p>
          </div>
        )}

        {/* Profile Header with Avatar */}
        <div className="bg-gradient-to-r from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8 mb-8">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
            {/* Avatar Section */}
            <div className="flex-shrink-0">
              <div className="relative">
                {currentAvatar ? (
                  <img
                    src={`http://localhost:5000${currentAvatar}`}
                    alt="Avatar"
                    className="w-40 h-40 rounded-full object-cover shadow-2xl border-4 border-purple-500/30"
                  />
                ) : (
                  <div className="w-40 h-40 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full flex items-center justify-center text-white text-5xl font-bold shadow-2xl border-4 border-purple-500/30">
                    {currentFirstName && currentLastName
                      ? `${currentFirstName[0]}${currentLastName[0]}`
                      : 'JD'}
                  </div>
                )}
                <label className="absolute bottom-3 right-3 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full transition-all duration-200 shadow-lg cursor-pointer hover:scale-110">
                  <Camera className="w-5 h-5" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="hidden"
                    disabled={avatarLoading}
                  />
                </label>
                {avatarLoading && (
                  <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1 text-center md:text-left">
              <h3 className="text-3xl font-bold text-white mb-2">
                {currentFirstName && currentLastName
                  ? `${currentFirstName} ${currentLastName}`
                  : 'Your Name'}
              </h3>
              <p className="text-xl text-gray-300 mb-1">@{currentUsername || 'username'}</p>
              <p className="text-lg text-gray-400 mb-4">{currentEmail || '<EMAIL>'}</p>
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 border border-purple-500/30 rounded-full">
                <span className="text-sm font-medium text-purple-300 uppercase tracking-wide">
                  {currentRole || 'User'}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-3">
                {avatarLoading ? 'Uploading avatar...' : 'Click the camera icon to change your avatar'}
              </p>
            </div>
          </div>
        </div>

        {/* Profile Form */}
        <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8">
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-white mb-2">Edit Profile Information</h3>
            <p className="text-gray-400">Update your personal details below. Leave fields empty to keep current values.</p>
            <div className="mt-3 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
              <p className="text-blue-300 text-sm">
                💡 <strong>Note:</strong> Avatar changes are saved immediately. Use the "Save Changes" button below for other profile fields.
              </p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* First Name */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-200 mb-2">
                First Name
              </label>
              <div className="bg-gray-800/30 rounded-lg p-3 mb-3">
                <span className="text-xs text-gray-400">Current: </span>
                <span className="text-white font-medium">{currentFirstName || 'Not set'}</span>
              </div>
              <input
                type="text"
                value={firstName}
                onChange={(e) => {
                  setFirstName(e.target.value);
                  if (fieldErrors.firstName) {
                    setFieldErrors((prev) => ({ ...prev, firstName: '' }));
                  }
                }}
                className={`w-full px-4 py-4 bg-gray-800/50 border-2 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 ${
                  fieldErrors.firstName
                    ? 'border-red-500 focus:ring-red-500/50'
                    : 'border-gray-600/50 focus:ring-purple-500/50 focus:border-purple-500/50'
                }`}
                placeholder="Enter new first name"
              />
              {fieldErrors.firstName && (
                <p className="text-red-400 text-sm mt-2 flex items-center">
                  <span className="w-4 h-4 mr-2">⚠️</span>
                  {fieldErrors.firstName}
                </p>
              )}
            </div>

            {/* Last Name */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-200 mb-2">
                Last Name
              </label>
              <div className="bg-gray-800/30 rounded-lg p-3 mb-3">
                <span className="text-xs text-gray-400">Current: </span>
                <span className="text-white font-medium">{currentLastName || 'Not set'}</span>
              </div>
              <input
                type="text"
                value={lastName}
                onChange={(e) => {
                  setLastName(e.target.value);
                  if (fieldErrors.lastName) {
                    setFieldErrors((prev) => ({ ...prev, lastName: '' }));
                  }
                }}
                className={`w-full px-4 py-4 bg-gray-800/50 border-2 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 ${
                  fieldErrors.lastName
                    ? 'border-red-500 focus:ring-red-500/50'
                    : 'border-gray-600/50 focus:ring-purple-500/50 focus:border-purple-500/50'
                }`}
                placeholder="Enter new last name"
              />
              {fieldErrors.lastName && (
                <p className="text-red-400 text-sm mt-2 flex items-center">
                  <span className="w-4 h-4 mr-2">⚠️</span>
                  {fieldErrors.lastName}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Username */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-200 mb-2">
                Username
              </label>
              <div className="bg-gray-800/30 rounded-lg p-3 mb-3">
                <span className="text-xs text-gray-400">Current: </span>
                <span className="text-white font-medium">@{currentUsername || 'Not set'}</span>
              </div>
              <input
                type="text"
                value={username}
                onChange={(e) => {
                  setUsername(e.target.value);
                  if (fieldErrors.username) {
                    setFieldErrors((prev) => ({ ...prev, username: '' }));
                  }
                }}
                className={`w-full px-4 py-4 bg-gray-800/50 border-2 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 ${
                  fieldErrors.username
                    ? 'border-red-500 focus:ring-red-500/50'
                    : 'border-gray-600/50 focus:ring-purple-500/50 focus:border-purple-500/50'
                }`}
                placeholder="Enter new username"
              />
              {fieldErrors.username && (
                <p className="text-red-400 text-sm mt-2 flex items-center">
                  <span className="w-4 h-4 mr-2">⚠️</span>
                  {fieldErrors.username}
                </p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-gray-200 mb-2">
                Email Address
              </label>
              <div className="bg-gray-800/30 rounded-lg p-3 mb-3">
                <span className="text-xs text-gray-400">Current: </span>
                <span className="text-white font-medium">{currentEmail || 'Not set'}</span>
              </div>
              <input
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (fieldErrors.email) {
                    setFieldErrors((prev) => ({ ...prev, email: '' }));
                  }
                }}
                className={`w-full px-4 py-4 bg-gray-800/50 border-2 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 ${
                  fieldErrors.email
                    ? 'border-red-500 focus:ring-red-500/50'
                    : 'border-gray-600/50 focus:ring-purple-500/50 focus:border-purple-500/50'
                }`}
                placeholder="Enter new email address"
              />
              {fieldErrors.email && (
                <p className="text-red-400 text-sm mt-2 flex items-center">
                  <span className="w-4 h-4 mr-2">⚠️</span>
                  {fieldErrors.email}
                </p>
              )}
            </div>
          </div>

          {/* Role Section */}
          <div className="mt-8 p-6 bg-gray-800/30 rounded-xl border border-gray-700/50">
            <label className="block text-sm font-semibold text-gray-200 mb-3">
              Account Role
            </label>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">
                    {currentRole === 'admin' ? 'A' : 'U'}
                  </span>
                </div>
                <div>
                  <p className="text-white font-medium capitalize">{currentRole || 'User'}</p>
                  <p className="text-xs text-gray-400">
                    {currentRole === 'admin' ? 'Full system access' : 'Standard user access'}
                  </p>
                </div>
              </div>
              <div className="text-xs text-gray-500 bg-gray-700/50 px-3 py-1 rounded-full">
                Read-only
              </div>
            </div>
            <p className="text-xs text-gray-400 mt-3 italic">
              Role changes require workspace administrator approval
            </p>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex flex-col items-center mt-10">
          <p className="text-gray-400 text-sm mb-3 text-center">
            Save changes to your profile information (Name, Username, Email)
          </p>
          <button
            onClick={handleSaveChanges}
            disabled={profileLoading}
            className="flex items-center space-x-3 bg-gradient-to-r from-purple-600 via-purple-700 to-cyan-600 hover:from-purple-700 hover:via-purple-800 hover:to-cyan-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl disabled:opacity-50 disabled:transform-none disabled:cursor-not-allowed min-w-[200px] justify-center"
          >
            {profileLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Saving Changes...</span>
              </>
            ) : (
              <>
                <Save className="w-5 h-5" />
                <span>Save Profile Changes</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => {

    return (
      <div className="space-y-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">Notification Preferences</h2>
          <p className="text-gray-400">Manage how and when you receive notifications</p>
        </div>

        {/* Success/Error Messages */}
        {notificationSuccess && (
          <div className="bg-green-900/50 border border-green-500 rounded-xl p-4">
            <p className="text-green-200">{notificationSuccess}</p>
          </div>
        )}

        {notificationError && (
          <div className="bg-red-900/50 border border-red-500 rounded-xl p-4">
            <p className="text-red-200">{notificationError}</p>
          </div>
        )}

        {/* Current Status Overview */}
        <div className="bg-gradient-to-r from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Bell className="w-6 h-6 mr-3 text-purple-400" />
            Current Notification Status
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                notificationSettings.email_notifications ? 'bg-green-500/20 border-2 border-green-500/50' : 'bg-gray-700/50 border-2 border-gray-600/50'
              }`}>
                <Mail className={`w-6 h-6 ${notificationSettings.email_notifications ? 'text-green-400' : 'text-gray-400'}`} />
              </div>
              <p className="text-sm text-gray-300">Email</p>
              <p className={`text-xs ${notificationSettings.email_notifications ? 'text-green-400' : 'text-gray-500'}`}>
                {notificationSettings.email_notifications ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                notificationSettings.toast_notifications ? 'bg-green-500/20 border-2 border-green-500/50' : 'bg-gray-700/50 border-2 border-gray-600/50'
              }`}>
                <Monitor className={`w-6 h-6 ${notificationSettings.toast_notifications ? 'text-green-400' : 'text-gray-400'}`} />
              </div>
              <p className="text-sm text-gray-300">Toasts</p>
              <p className={`text-xs ${notificationSettings.toast_notifications ? 'text-green-400' : 'text-gray-500'}`}>
                {notificationSettings.toast_notifications ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                notificationSettings.security_alerts ? 'bg-green-500/20 border-2 border-green-500/50' : 'bg-gray-700/50 border-2 border-gray-600/50'
              }`}>
                <Shield className={`w-6 h-6 ${notificationSettings.security_alerts ? 'text-green-400' : 'text-gray-400'}`} />
              </div>
              <p className="text-sm text-gray-300">Security</p>
              <p className={`text-xs ${notificationSettings.security_alerts ? 'text-green-400' : 'text-gray-500'}`}>
                {notificationSettings.security_alerts ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className="text-center">
              <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center ${
                notificationSettings.analysis_completed ? 'bg-green-500/20 border-2 border-green-500/50' : 'bg-gray-700/50 border-2 border-gray-600/50'
              }`}>
                <Activity className={`w-6 h-6 ${notificationSettings.analysis_completed ? 'text-green-400' : 'text-gray-400'}`} />
              </div>
              <p className="text-sm text-gray-300">Analytics</p>
              <p className={`text-xs ${notificationSettings.analysis_completed ? 'text-green-400' : 'text-gray-500'}`}>
                {notificationSettings.analysis_completed ? 'Enabled' : 'Disabled'}
              </p>
            </div>
          </div>
        </div>

        {/* General Notification Settings */}
        <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8">
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
            <Mail className="w-6 h-6 mr-3 text-blue-400" />
            General Notifications
          </h3>

          <div className="space-y-6">
            {/* Email Notifications */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <Mail className="w-6 h-6 text-blue-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">Email Notifications</h4>
                  <p className="text-sm text-gray-400">Receive notifications via email</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.email_notifications}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      email_notifications: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* Toast Notifications */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                  <Monitor className="w-6 h-6 text-purple-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">Toast Notifications</h4>
                  <p className="text-sm text-gray-400">Show popup notifications in the platform</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.toast_notifications}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      toast_notifications: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Security & System Notifications */}
        <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8">
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
            <Shield className="w-6 h-6 mr-3 text-red-400" />
            Security & System Notifications
          </h3>

          <div className="space-y-6">
            {/* Security Alerts */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center">
                  <Shield className="w-6 h-6 text-red-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">Security Alerts</h4>
                  <p className="text-sm text-gray-400">Login attempts, password changes, suspicious activity</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.security_alerts}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      security_alerts: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
              </label>
            </div>

            {/* System Updates */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-cyan-500/20 rounded-xl flex items-center justify-center">
                  <Zap className="w-6 h-6 text-cyan-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">System Updates</h4>
                  <p className="text-sm text-gray-400">Platform updates, maintenance notifications</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.system_updates}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      system_updates: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
              </label>
            </div>

            {/* Analysis Completed */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                  <Activity className="w-6 h-6 text-green-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">Analysis Completed</h4>
                  <p className="text-sm text-gray-400">AI analysis results and reports</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.analysis_completed}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      analysis_completed: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
            </div>

            {/* Login Alerts */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center">
                  <Key className="w-6 h-6 text-yellow-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">Login Alerts</h4>
                  <p className="text-sm text-gray-400">New device logins and session activity</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.login_alerts}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      login_alerts: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
              </label>
            </div>

            {/* Profile Changes */}
            <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                  <User className="w-6 h-6 text-purple-400" />
                </div>
                <div>
                  <h4 className="text-white font-semibold">Profile Changes</h4>
                  <p className="text-sm text-gray-400">Account updates and modifications</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.profile_changes}
                  onChange={(e) =>
                    setNotificationSettings((prev) => ({
                      ...prev,
                      profile_changes: e.target.checked,
                    }))
                  }
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Admin-only Incident Notifications */}
        {isAdmin && (
          <div className="bg-gradient-to-br from-orange-900/20 to-red-900/20 backdrop-blur-xl border border-orange-500/30 rounded-3xl p-8">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
              <MessageSquare className="w-6 h-6 mr-3 text-orange-400" />
              Admin Incident Notifications
            </h3>

            <div className="space-y-6">
              {/* Email Incidents */}
              <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-orange-400" />
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">Email Incident Alerts</h4>
                    <p className="text-sm text-gray-400">Critical system incidents via email</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationSettings.email_enabled}
                    onChange={(e) =>
                      setNotificationSettings((prev) => ({
                        ...prev,
                        email_enabled: e.target.checked,
                      }))
                    }
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </label>
              </div>

              {/* Telegram Incidents */}
              <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-cyan-500/20 rounded-xl flex items-center justify-center">
                    <MessageSquare className="w-6 h-6 text-cyan-400" />
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">Telegram Incident Alerts</h4>
                    <p className="text-sm text-gray-400">Real-time incidents via Telegram</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notificationSettings.telegram_enabled}
                    onChange={(e) =>
                      setNotificationSettings((prev) => ({
                        ...prev,
                        telegram_enabled: e.target.checked,
                      }))
                    }
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={saveNotificationSettings}
            disabled={notificationLoading}
            className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:transform-none"
          >
            {notificationLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>Save Notification Settings</span>
          </button>
        </div>
      </div>
    );
  };

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
        <Shield className="w-8 h-8 mr-3 text-purple-400" />
        Security Settings
      </h2>

      {/* Loading State */}
      {securityLoading && (
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400">Loading security settings...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {securityError && (
        <div className="bg-red-900/20 border border-red-500/50 rounded-2xl p-4">
          <p className="text-red-400">❌ {securityError}</p>
        </div>
      )}

      {/* Success State */}
      {securitySuccess && (
        <div className="bg-green-900/20 border border-green-500/50 rounded-2xl p-4">
          <p className="text-green-400">✅ {securitySuccess}</p>
        </div>
      )}

      {/* Password Change Section */}
      <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
          <Key className="w-6 h-6 mr-3 text-yellow-400" />
          Change Password
        </h3>

        {securitySettings.password_last_changed && (
          <div className="mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-xl">
            <p className="text-blue-300 text-sm">
              🕒 Password last changed: {new Date(securitySettings.password_last_changed).toLocaleDateString()}
            </p>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
            <input
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              placeholder="Enter your current password"
              className="w-full bg-gray-800/50 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">New Password</label>
            <input
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Enter your new password"
              className="w-full bg-gray-800/50 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm your new password"
              className="w-full bg-gray-800/50 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
            />
          </div>

          <button
            onClick={changePassword}
            disabled={securityLoading || !currentPassword || !newPassword || !confirmPassword}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center"
          >
            {securityLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Changing Password...
              </>
            ) : (
              <>
                <Key className="w-5 h-5 mr-2" />
                Change Password
              </>
            )}
          </button>
        </div>
      </div>

      {/* Active Sessions Section */}
      <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white flex items-center">
            <Monitor className="w-6 h-6 mr-3 text-green-400" />
            Active Sessions
          </h3>
          <button
            onClick={terminateAllSessions}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-xl transition-colors text-sm font-medium"
          >
            Terminate All Others
          </button>
        </div>

        <div className="space-y-4">
          {securitySettings.login_sessions.length > 0 ? (
            securitySettings.login_sessions.map((session, index) => (
              <div
                key={session.id}
                className="bg-gray-800/30 border border-gray-700/50 rounded-xl p-4 flex items-center justify-between"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                    <Monitor className="w-6 h-6 text-green-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">{session.device}</p>
                    <p className="text-sm text-gray-400">
                      {session.ip_address} • Last active: {new Date(session.last_active).toLocaleString()}
                    </p>
                    {session.location && (
                      <p className="text-xs text-gray-500">{session.location}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {session.is_current ? (
                    <span className="text-green-400 text-sm font-medium">Current Session</span>
                  ) : (
                    <button
                      onClick={() => terminateSession(session.id)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg transition-colors text-sm"
                    >
                      Terminate
                    </button>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-400">No active sessions found</p>
            </div>
          )}
        </div>
      </div>

      {/* Security Policy Section */}
      <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl border border-gray-700/50 rounded-3xl p-8">
        <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
          <Shield className="w-6 h-6 mr-3 text-blue-400" />
          Security Policy
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Account Lockout */}
          <div className="bg-gray-800/30 rounded-xl p-4">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <Lock className="w-5 h-5 mr-2 text-red-400" />
              Account Lockout
            </h4>
            <div className="space-y-2 text-sm">
              <p className="text-gray-300">
                Status: <span className={securitySettings.account_lockout.enabled ? 'text-green-400' : 'text-red-400'}>
                  {securitySettings.account_lockout.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </p>
              <p className="text-gray-300">
                Max attempts: <span className="text-blue-400">{securitySettings.account_lockout.max_attempts}</span>
              </p>
              <p className="text-gray-300">
                Lockout duration: <span className="text-blue-400">{securitySettings.account_lockout.lockout_duration} minutes</span>
              </p>
            </div>
          </div>

          {/* Password Policy */}
          <div className="bg-gray-800/30 rounded-xl p-4">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <Key className="w-5 h-5 mr-2 text-yellow-400" />
              Password Policy
            </h4>
            <div className="space-y-2 text-sm">
              <p className="text-gray-300">
                Min length: <span className="text-blue-400">{securitySettings.password_policy.min_length} characters</span>
              </p>
              <p className="text-gray-300">
                Uppercase: <span className={securitySettings.password_policy.require_uppercase ? 'text-green-400' : 'text-red-400'}>
                  {securitySettings.password_policy.require_uppercase ? 'Required' : 'Optional'}
                </span>
              </p>
              <p className="text-gray-300">
                Numbers: <span className={securitySettings.password_policy.require_numbers ? 'text-green-400' : 'text-red-400'}>
                  {securitySettings.password_policy.require_numbers ? 'Required' : 'Optional'}
                </span>
              </p>
              <p className="text-gray-300">
                Symbols: <span className={securitySettings.password_policy.require_symbols ? 'text-green-400' : 'text-red-400'}>
                  {securitySettings.password_policy.require_symbols ? 'Required' : 'Optional'}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOtherTabs = (tabId: string) => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
        <Shield className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-white mb-2">
        {tabs.find((tab) => tab.id === tabId)?.label} Settings
      </h3>
      <p className="text-gray-400">Cette section sera implémentée prochainement.</p>
    </div>
  );

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-64 bg-gray-900/60 backdrop-blur-xl border-r border-gray-700/50 p-4">
        <h1 className="text-xl font-bold text-white mb-6">Settings</h1>
        <nav className="space-y-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        {activeTab === 'profile' && renderProfileTab()}
        {activeTab === 'notifications' && renderNotificationsTab()}
        {activeTab === 'security' && renderSecurityTab()}
        {!['profile', 'notifications', 'security'].includes(activeTab) &&
          renderOtherTabs(activeTab)}
      </div>
    </div>
  );
}
