// ✅ Login.tsx
import React, { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Shield, CheckCircle } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthContext } from '../contexts/AuthContext';
import Logo from '../components/Logo';
import { TwoFactorVerification } from '../components/auth/TwoFactorVerification';

export default function Login() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Admin123!');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [alert, setAlert] = useState<{ type: 'error' | 'success'; message: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [show2FA, setShow2FA] = useState(false);
  const [twoFactorError, setTwoFactorError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { login } = useAuthContext();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading) return;
    setAlert(null);
    setIsLoading(true);

    try {
      console.log('🚀 Login attempt:', { email, password, rememberMe });

      const response = await fetch('http://localhost:5000/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          remember_me: rememberMe,
        }),
      });

      const data = await response.json();
      console.log('📡 Login response:', data);

      if (response.ok) {
        if (data['2fa']) {
          // 2FA required - show verification screen
          console.log('🔐 2FA required, showing verification screen');
          setShow2FA(true);
          setAlert({ type: 'success', message: data.msg || '2FA code sent to your email' });
          return;
        } else if (data.token) {
          // Normal login - no 2FA required
          const userInfo = data.user;

        console.log('🔑 Token received:', data.token.substring(0, 50) + '...');
        console.log(
          '🔄 Refresh token received:',
          data.refresh_token ? data.refresh_token.substring(0, 50) + '...' : 'Not provided'
        );
        console.log('👤 User information:', userInfo);
        console.log('⏰ Remember me:', rememberMe);

        // Use the authentication context login method
        // which automatically handles token and user data storage
        login(data.token, userInfo, data.refresh_token, rememberMe);

        setAlert({ type: 'success', message: 'Login successful!' });

        // Redirect to pentesting after 1 second
        setTimeout(() => {
          navigate('/app');
        }, 1000);
      } else {
        setAlert({
          type: 'error',
          message: data.msg || data.message || 'Login error. Please check your credentials.',
        });
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setAlert({
        type: 'error',
        message: 'Connection error. Please check your network connection.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTwoFactorVerify = async (code: string) => {
    setTwoFactorError(null);
    setIsLoading(true);

    try {
      console.log('🔐 Verifying 2FA code:', code);

      const response = await fetch('http://localhost:5000/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          otp: code,
        }),
      });

      const data = await response.json();
      console.log('📡 2FA verification response:', data);

      if (response.ok && data.token) {
        // 2FA verification successful
        const userInfo = data.user;

        console.log('✅ 2FA verification successful, logging in user:', userInfo);

        // Store token and user info
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(userInfo));

        // Update auth context
        login(userInfo, data.token);

        // Navigate to dashboard
        navigate('/dashboard');
      } else {
        // 2FA verification failed
        setTwoFactorError(data.msg || 'Invalid verification code');
      }
    } catch (error) {
      console.error('❌ 2FA verification error:', error);
      setTwoFactorError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setShow2FA(false);
    setTwoFactorError(null);
    setAlert(null);
  };

  // Show 2FA verification screen if required
  if (show2FA) {
    return (
      <TwoFactorVerification
        email={email}
        onVerify={handleTwoFactorVerify}
        onBack={handleBackToLogin}
        loading={isLoading}
        error={twoFactorError}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 opacity-20">
          <Shield className="w-16 h-16 text-purple-300 animate-pulse" />
        </div>
        <div className="absolute top-40 right-32 opacity-15">
          <Shield
            className="w-12 h-12 text-purple-400 animate-pulse"
            style={{ animationDelay: '1s' }}
          />
        </div>
        <div className="absolute bottom-32 left-16 opacity-10">
          <Shield
            className="w-20 h-20 text-purple-200 animate-pulse"
            style={{ animationDelay: '2s' }}
          />
        </div>
        <div className="absolute bottom-20 right-20 opacity-25">
          <Shield
            className="w-14 h-14 text-purple-300 animate-pulse"
            style={{ animationDelay: '0.5s' }}
          />
        </div>
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '1.5s' }}
        ></div>
      </div>

      <div className="relative z-10 min-h-screen flex">
        {/* Left side - Illustration */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12">
          <div className="relative w-full h-full flex items-center justify-center">
            <div className="relative">
              <div className="w-80 h-96 bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-700 rounded-t-full rounded-b-3xl shadow-[0_0_80px_rgba(139,92,246,0.6)] border-4 border-purple-300/30">
                <div className="absolute inset-6 bg-gradient-to-br from-purple-400/20 via-pink-400/10 to-transparent rounded-t-full rounded-b-3xl"></div>
                <div className="absolute inset-8 border-2 border-purple-300/20 rounded-t-full rounded-b-3xl">
                  <div className="absolute inset-6 border border-purple-300/10 rounded-t-full rounded-b-3xl"></div>
                </div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-[0_0_40px_rgba(6,182,212,0.8)] animate-pulse">
                  <Shield className="w-20 h-20 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="absolute -top-8 -left-8 w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl flex items-center justify-center shadow-[0_0_30px_rgba(34,197,94,0.6)] animate-float">
                <CheckCircle className="w-12 h-12 text-white" />
              </div>
              <div
                className="absolute -top-4 -right-12 w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-600 rounded-xl flex items-center justify-center shadow-[0_0_25px_rgba(59,130,246,0.6)] animate-float"
                style={{ animationDelay: '1s' }}
              >
                <Lock className="w-10 h-10 text-white" />
              </div>
              <div
                className="absolute -bottom-8 -right-8 w-24 h-24 bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl flex items-center justify-center shadow-[0_0_35px_rgba(251,146,60,0.6)] animate-float"
                style={{ animationDelay: '2s' }}
              >
                <Eye className="w-14 h-14 text-white" />
              </div>
              <div
                className="absolute bottom-1/3 -left-16 w-18 h-18 bg-gradient-to-br from-pink-400 to-purple-500 rounded-xl flex items-center justify-center shadow-[0_0_28px_rgba(236,72,153,0.6)] animate-float"
                style={{ animationDelay: '0.5s' }}
              >
                <Mail className="w-10 h-10 text-white" />
              </div>
              <div
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] border-4 border-purple-300/20 rounded-full animate-spin"
                style={{ animationDuration: '30s' }}
              >
                <div
                  className="absolute inset-12 border-2 border-cyan-300/15 rounded-full animate-spin"
                  style={{ animationDuration: '20s', animationDirection: 'reverse' }}
                ></div>
                <div
                  className="absolute inset-24 border border-green-300/10 rounded-full animate-spin"
                  style={{ animationDuration: '15s' }}
                ></div>
              </div>
              <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 w-96 h-8 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent rounded-full blur-2xl"></div>
            </div>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <div className="bg-purple-900/40 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-8 shadow-2xl">
              <div className="text-center mb-8">
                <div className="flex justify-center mb-4">
                  <Logo size="lg" showText={false} />
                </div>
                <h2 className="text-2xl font-bold text-white mb-2">Login</h2>
                <p className="text-purple-200">Access your secure account</p>
              </div>

              {alert && (
                <div
                  className={`mb-4 p-4 rounded-lg ${alert.type === 'error' ? 'bg-red-500/20 border border-red-500/30 text-red-200' : 'bg-green-500/20 border border-green-500/30 text-green-200'}`}
                >
                  <div className="flex justify-between items-center">
                    <span>{alert.message}</span>
                    <button
                      onClick={() => setAlert(null)}
                      className="text-white/60 hover:text-white"
                    >
                      ×
                    </button>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Mail className="w-5 h-5 text-purple-400" />
                    </div>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">Password</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Lock className="w-5 h-5 text-purple-400" />
                    </div>
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-12 pr-12 py-3 bg-purple-800/30 border border-purple-500/30 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      placeholder="••••••••••"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center text-purple-400 hover:text-purple-300 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  <div className="mt-2">
                    <Link
                      to="/auth/forgot-password"
                      className="text-sm text-purple-300 hover:text-white transition-colors"
                    >
                      Forgot password?
                    </Link>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="rememberMe"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="w-4 h-4 text-purple-600 bg-purple-800/30 border-purple-500/30 rounded focus:ring-purple-500 focus:ring-2"
                  />
                  <label htmlFor="rememberMe" className="ml-3 text-sm text-purple-200">
                    Remember me (24h)
                  </label>
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-purple-900"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Logging in...
                    </div>
                  ) : (
                    'Login'
                  )}
                </button>
              </form>

              <div className="mt-8 text-center">
                <p className="text-purple-200">
                  Don't have an account?{' '}
                  <Link
                    to="/auth/register"
                    className="text-purple-300 hover:text-white font-medium transition-colors"
                  >
                    Create account
                  </Link>
                </p>
              </div>

              <div className="mt-8 text-center">
                <p className="text-xs text-purple-400">
                  © PICA - Automated Cybersecurity Platform
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
