import { useCallback, useState } from 'react';
import { notificationService, NotificationPreferences } from '../services/notificationService';
import { useNotificationSystem } from './useNotificationSystem';

export const useNotifications = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);

  // Initialiser le système de notifications
  const {
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLoginAlert,
    sendProfileChange,
  } = useNotificationSystem();

  /**
   * Met à jour les préférences dans le service
   */
  const updatePreferences = useCallback((newPreferences: NotificationPreferences) => {
    notificationService.updatePreferences(newPreferences);
    setPreferences(newPreferences);
  }, []);

  /**
   * Teste les notifications (pour debug)
   */
  const testNotification = useCallback(async (type: 'security' | 'system' | 'analysis' | 'login' | 'profile') => {
    const testMessages = {
      security: {
        title: '🛡️ Security Alert Test',
        message: 'This is a test security alert - Suspicious login attempt detected from new device',
        notificationType: 'security_alerts'
      },
      system: {
        title: '🔄 System Update Test',
        message: 'This is a test system update - PICA platform has been updated to version 2.1.0',
        notificationType: 'system_updates'
      },
      analysis: {
        title: '📊 Analysis Complete Test',
        message: 'This is a test analysis notification - Your vulnerability scan has finished processing',
        notificationType: 'analysis_completed'
      },
      login: {
        title: '🔑 New Login Test',
        message: 'This is a test login alert - Login detected from Chrome on Windows 11',
        notificationType: 'login_alerts'
      },
      profile: {
        title: '👤 Profile Updated Test',
        message: 'This is a test profile notification - Your profile information has been successfully updated',
        notificationType: 'profile_changes'
      }
    };

    const { title, message, notificationType } = testMessages[type];

    // Utiliser directement le service de notifications pour avoir toast + base de données
    await notificationService.sendNotification({
      type: notificationType as any,
      title,
      message,
      data: { test: true, timestamp: new Date().toISOString() }
    });

  }, []);

  return {
    preferences,
    updatePreferences,
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLoginAlert,
    sendProfileChange,
    testNotification,
  };
};

export default useNotifications;
