import { useCallback, useState } from 'react';
import { notificationService, NotificationPreferences } from '../services/notificationService';
import { useNotificationSystem } from './useNotificationSystem';

export const useNotifications = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);

  // Initialiser le système de notifications
  const {
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLoginAlert,
    sendProfileChange,
  } = useNotificationSystem();

  /**
   * Met à jour les préférences dans le service
   */
  const updatePreferences = useCallback((newPreferences: NotificationPreferences) => {
    notificationService.updatePreferences(newPreferences);
    setPreferences(newPreferences);
  }, []);

  /**
   * Teste les notifications (pour debug)
   */
  const testNotification = useCallback(async (type: 'security' | 'system' | 'analysis' | 'login' | 'profile') => {
    const testMessages = {
      security: {
        title: '🛡️ Security Alert',
        message: 'Suspicious login attempt detected from new device'
      },
      system: {
        title: '🔄 System Update',
        message: 'PICA platform has been updated to version 2.1.0'
      },
      analysis: {
        title: '📊 Analysis Complete',
        message: 'Your vulnerability scan has finished processing'
      },
      login: {
        title: '🔑 New Login',
        message: 'Login detected from Chrome on Windows 11'
      },
      profile: {
        title: '👤 Profile Updated',
        message: 'Your profile information has been successfully updated'
      }
    };

    const { title, message } = testMessages[type];

    switch (type) {
      case 'security':
        await sendSecurityAlert(title, message);
        break;
      case 'system':
        await sendSystemUpdate(title, message);
        break;
      case 'analysis':
        await sendAnalysisCompleted(title, message);
        break;
      case 'login':
        await sendLoginAlert(title, message);
        break;
      case 'profile':
        await sendProfileChange(title, message);
        break;
    }
  }, [sendSecurityAlert, sendSystemUpdate, sendAnalysisCompleted, sendLoginAlert, sendProfileChange]);

  return {
    preferences,
    updatePreferences,
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLoginAlert,
    sendProfileChange,
    testNotification,
  };
};

export default useNotifications;
