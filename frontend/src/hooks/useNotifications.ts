import { useCallback, useEffect, useState } from 'react';
import { notificationService, NotificationPreferences } from '../services/notificationService';

export const useNotifications = () => {
  const [browserPermission, setBrowserPermission] = useState<NotificationPermission>('default');
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);

  useEffect(() => {
    // Vérifier l'état initial des permissions
    if ('Notification' in window) {
      setBrowserPermission(Notification.permission);
    }
  }, []);

  /**
   * Demande la permission pour les notifications browser
   */
  const requestBrowserPermission = useCallback(async () => {
    const granted = await notificationService.requestBrowserPermission();
    if ('Notification' in window) {
      setBrowserPermission(Notification.permission);
    }
    return granted;
  }, []);

  /**
   * Met à jour les préférences dans le service
   */
  const updatePreferences = useCallback((newPreferences: NotificationPreferences) => {
    notificationService.updatePreferences(newPreferences);
    setPreferences(newPreferences);
  }, []);

  /**
   * Méthodes de convenance pour envoyer des notifications
   */
  const sendSecurityAlert = useCallback(async (title: string, message: string, data?: any) => {
    await notificationService.sendSecurityAlert(title, message, data);
  }, []);

  const sendSystemUpdate = useCallback(async (title: string, message: string, data?: any) => {
    await notificationService.sendSystemUpdate(title, message, data);
  }, []);

  const sendAnalysisCompleted = useCallback(async (title: string, message: string, data?: any) => {
    await notificationService.sendAnalysisCompleted(title, message, data);
  }, []);

  const sendLoginAlert = useCallback(async (title: string, message: string, data?: any) => {
    await notificationService.sendLoginAlert(title, message, data);
  }, []);

  const sendProfileChange = useCallback(async (title: string, message: string, data?: any) => {
    await notificationService.sendProfileChange(title, message, data);
  }, []);

  /**
   * Teste les notifications (pour debug)
   */
  const testNotification = useCallback(async (type: 'security' | 'system' | 'analysis' | 'login' | 'profile') => {
    const testMessages = {
      security: {
        title: '🛡️ Security Alert',
        message: 'Suspicious login attempt detected from new device'
      },
      system: {
        title: '🔄 System Update',
        message: 'PICA platform has been updated to version 2.1.0'
      },
      analysis: {
        title: '📊 Analysis Complete',
        message: 'Your vulnerability scan has finished processing'
      },
      login: {
        title: '🔑 New Login',
        message: 'Login detected from Chrome on Windows 11'
      },
      profile: {
        title: '👤 Profile Updated',
        message: 'Your profile information has been successfully updated'
      }
    };

    const { title, message } = testMessages[type];

    switch (type) {
      case 'security':
        await sendSecurityAlert(title, message);
        break;
      case 'system':
        await sendSystemUpdate(title, message);
        break;
      case 'analysis':
        await sendAnalysisCompleted(title, message);
        break;
      case 'login':
        await sendLoginAlert(title, message);
        break;
      case 'profile':
        await sendProfileChange(title, message);
        break;
    }
  }, [sendSecurityAlert, sendSystemUpdate, sendAnalysisCompleted, sendLoginAlert, sendProfileChange]);

  return {
    browserPermission,
    preferences,
    requestBrowserPermission,
    updatePreferences,
    sendSecurityAlert,
    sendSystemUpdate,
    sendAnalysisCompleted,
    sendLoginAlert,
    sendProfileChange,
    testNotification,
  };
};

export default useNotifications;
