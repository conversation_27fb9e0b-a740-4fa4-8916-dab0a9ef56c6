import { useEffect } from 'react';
import { useNotifications as useNotificationContext } from '../contexts/NotificationContext';
import { useAppDispatch } from '../store/hooks';
import { addAlert } from '../store/slices/alertSlice';
import { notificationService } from '../services/notificationService';

/**
 * Hook pour connecter le service de notifications aux systèmes existants
 * (NotificationContext pour les toasts + Redux pour le header)
 */
export const useNotificationSystem = () => {
  const { showSuccess, showError, showWarning, showInfo } = useNotificationContext();
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Enregistrer les callbacks pour les toasts
    notificationService.setToastCallbacks({
      showSuccess,
      showError,
      showWarning,
      showInfo,
    });

    // Enregistrer les callbacks pour les notifications header
    notificationService.setHeaderCallbacks({
      addNotification: (notification) => {
        // Ajouter à Redux pour le header
        dispatch(addAlert({
          id: notification.id,
          type: notification.type === 'security_alerts' ? 'error' :
                notification.type === 'analysis_completed' ? 'success' :
                notification.type === 'login_alerts' ? 'warning' : 'info',
          title: notification.title,
          message: notification.message,
          timestamp: notification.timestamp,
        }));
      },
    });

    console.log('✅ Notification system initialized');
  }, [showSuccess, showError, showWarning, showInfo, dispatch]);

  return {
    // Méthodes de convenance
    sendSecurityAlert: notificationService.sendSecurityAlert.bind(notificationService),
    sendSystemUpdate: notificationService.sendSystemUpdate.bind(notificationService),
    sendAnalysisCompleted: notificationService.sendAnalysisCompleted.bind(notificationService),
    sendLoginAlert: notificationService.sendLoginAlert.bind(notificationService),
    sendProfileChange: notificationService.sendProfileChange.bind(notificationService),
  };
};

export default useNotificationSystem;
