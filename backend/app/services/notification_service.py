"""
Service de notifications pour PICA
Gère l'envoi de notifications selon les préférences utilisateur
"""

from flask import current_app
from ..extensions import mongo
from bson import ObjectId
from datetime import datetime
import requests
import json

class NotificationService:
    
    @staticmethod
    def get_user_preferences(user_id):
        """
        Récupère les préférences de notifications d'un utilisateur
        """
        try:
            # Essayer avec ObjectId d'abord
            try:
                user_object_id = ObjectId(user_id)
                user = mongo.db.users.find_one({"_id": user_object_id})
            except:
                # Si ça échoue, essayer avec le username
                user = mongo.db.users.find_one({"username": user_id})
            
            if not user:
                print(f"❌ User not found: {user_id}")
                return None
            
            # Préférences par défaut
            default_preferences = {
                'security_alerts': True,
                'system_updates': True,
                'analysis_completed': True,
                'login_alerts': False,
                'profile_changes': True,
                'email_notifications': True,
                'toast_notifications': True,
            }
            
            preferences = user.get('notification_preferences', default_preferences)
            print(f"✅ User preferences loaded for {user_id}: {preferences}")
            return preferences
            
        except Exception as e:
            print(f"❌ Error getting user preferences: {e}")
            return None
    
    @staticmethod
    def is_notification_enabled(user_id, notification_type):
        """
        Vérifie si un type de notification est activé pour un utilisateur
        """
        preferences = NotificationService.get_user_preferences(user_id)
        if not preferences:
            return False
        
        return preferences.get(notification_type, False)
    
    @staticmethod
    def store_notification(user_id, notification_type, title, message, data=None):
        """
        Stocke une notification en base de données pour l'affichage dans l'interface
        """
        try:
            notification = {
                'user_id': user_id,
                'type': notification_type,
                'title': title,
                'message': message,
                'data': data or {},
                'read': False,
                'created_at': datetime.utcnow(),
            }
            
            result = mongo.db.notifications.insert_one(notification)
            print(f"✅ Notification stored: {result.inserted_id}")
            return str(result.inserted_id)
            
        except Exception as e:
            print(f"❌ Error storing notification: {e}")
            return None
    
    @staticmethod
    def send_security_alert(user_id, title, message, data=None):
        """
        Envoie une alerte de sécurité
        """
        if not NotificationService.is_notification_enabled(user_id, 'security_alerts'):
            print(f"🔕 Security alerts disabled for user {user_id}")
            return False
        
        print(f"🛡️ Sending security alert to {user_id}: {title}")
        
        # Stocker en base pour l'interface
        notification_id = NotificationService.store_notification(
            user_id, 'security_alerts', title, message, data
        )
        
        # Ici on pourrait ajouter d'autres canaux (email, etc.)
        
        return notification_id is not None
    
    @staticmethod
    def send_login_alert(user_id, title, message, data=None):
        """
        Envoie une alerte de connexion
        """
        if not NotificationService.is_notification_enabled(user_id, 'login_alerts'):
            print(f"🔕 Login alerts disabled for user {user_id}")
            return False
        
        print(f"🔑 Sending login alert to {user_id}: {title}")
        
        # Stocker en base pour l'interface
        notification_id = NotificationService.store_notification(
            user_id, 'login_alerts', title, message, data
        )
        
        return notification_id is not None
    
    @staticmethod
    def send_profile_change_alert(user_id, title, message, data=None):
        """
        Envoie une alerte de changement de profil
        """
        if not NotificationService.is_notification_enabled(user_id, 'profile_changes'):
            print(f"🔕 Profile change alerts disabled for user {user_id}")
            return False
        
        print(f"👤 Sending profile change alert to {user_id}: {title}")
        
        # Stocker en base pour l'interface
        notification_id = NotificationService.store_notification(
            user_id, 'profile_changes', title, message, data
        )
        
        return notification_id is not None
    
    @staticmethod
    def send_analysis_completed(user_id, title, message, data=None):
        """
        Envoie une notification d'analyse terminée
        """
        if not NotificationService.is_notification_enabled(user_id, 'analysis_completed'):
            print(f"🔕 Analysis completed alerts disabled for user {user_id}")
            return False
        
        print(f"📊 Sending analysis completed notification to {user_id}: {title}")
        
        # Stocker en base pour l'interface
        notification_id = NotificationService.store_notification(
            user_id, 'analysis_completed', title, message, data
        )
        
        return notification_id is not None
    
    @staticmethod
    def send_system_update(user_id, title, message, data=None):
        """
        Envoie une notification de mise à jour système
        """
        if not NotificationService.is_notification_enabled(user_id, 'system_updates'):
            print(f"🔕 System updates disabled for user {user_id}")
            return False
        
        print(f"🔄 Sending system update notification to {user_id}: {title}")
        
        # Stocker en base pour l'interface
        notification_id = NotificationService.store_notification(
            user_id, 'system_updates', title, message, data
        )
        
        return notification_id is not None
    
    @staticmethod
    def get_user_notifications(user_id, limit=50, unread_only=False):
        """
        Récupère les notifications d'un utilisateur
        """
        try:
            query = {'user_id': user_id}
            if unread_only:
                query['read'] = False
            
            notifications = list(mongo.db.notifications.find(query)
                                .sort('created_at', -1)
                                .limit(limit))
            
            # Convertir ObjectId en string pour JSON
            for notification in notifications:
                notification['_id'] = str(notification['_id'])
                notification['created_at'] = notification['created_at'].isoformat()
            
            return notifications
            
        except Exception as e:
            print(f"❌ Error getting user notifications: {e}")
            return []
    
    @staticmethod
    def mark_notification_read(notification_id):
        """
        Marque une notification comme lue
        """
        try:
            result = mongo.db.notifications.update_one(
                {'_id': ObjectId(notification_id)},
                {'$set': {'read': True, 'read_at': datetime.utcnow()}}
            )
            return result.modified_count > 0
        except Exception as e:
            print(f"❌ Error marking notification as read: {e}")
            return False
    
    @staticmethod
    def mark_all_read(user_id):
        """
        Marque toutes les notifications d'un utilisateur comme lues
        """
        try:
            result = mongo.db.notifications.update_many(
                {'user_id': user_id, 'read': False},
                {'$set': {'read': True, 'read_at': datetime.utcnow()}}
            )
            return result.modified_count
        except Exception as e:
            print(f"❌ Error marking all notifications as read: {e}")
            return 0
