from flask import Blueprint, request, jsonify, send_from_directory
from ..controllers.signup_controller import register, confirm_email
from ..controllers.login_controller import login, verify_otp, toggle_2fa, refresh_token, logout
from ..controllers.password_controller import send_reset_link, reset_password
from flask_jwt_extended import jwt_required, get_jwt_identity
from ..extensions import mongo
from ..utils.decorators import handle_options
from ..services.notification_service import NotificationService
from bson import ObjectId
import os
from werkzeug.utils import secure_filename
import uuid

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

auth_bp.route('/register', methods=['POST'])(register)
auth_bp.route('/confirm-email', methods=['GET'])(confirm_email)
auth_bp.route('/login', methods=['POST'])(login)
auth_bp.route('/login/verify', methods=['POST'])(verify_otp)
auth_bp.route('/refresh', methods=['POST'])(refresh_token)
auth_bp.route('/logout', methods=['POST'])(logout)
auth_bp.route('/2fa-toggle', methods=['PATCH'])(jwt_required()(toggle_2fa))

# Profile update endpoint
@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Update user profile information"""
    from flask_jwt_extended import get_jwt_identity
    from app.utils.helpers import is_valid_email, is_valid_name
    from bson import ObjectId

    try:
        user_id = get_jwt_identity()
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        print(f"🔍 Profile update request for user: {user_id}")
        print(f"🔍 JWT claims: {claims}")

        data = request.get_json()
        print(f"🔍 Request data: {data}")

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Fields that can be updated
        allowed_fields = ['first_name', 'last_name', 'email', 'username', 'notification_preferences']
        update_fields = {}

        # Validate and prepare update fields
        for field in allowed_fields:
            if field in data:
                value = data[field]

                # Validation
                if field in ['first_name', 'last_name'] and not is_valid_name(value):
                    return jsonify({"error": f"Invalid {field} format"}), 400
                elif field == 'email' and not is_valid_email(value):
                    return jsonify({"error": "Invalid email format"}), 400
                elif field == 'username' and len(value.strip()) < 3:
                    return jsonify({"error": "Username must be at least 3 characters"}), 400

                update_fields[field] = value

        if not update_fields:
            return jsonify({"error": "No valid fields to update"}), 400

        # First, find the current user to get their ObjectId
        # The user_id from get_jwt_identity() is already the ObjectId string
        try:
            print(f"🔍 Using user ID from JWT identity: {user_id}")
            current_user_id = ObjectId(user_id)
            current_user = mongo.db.users.find_one({"_id": current_user_id})
        except Exception as e:
            print(f"🔍 Failed to use as ObjectId, trying username search: {e}")
            current_user = mongo.db.users.find_one({"username": user_id})
            if current_user:
                current_user_id = current_user["_id"]
            else:
                current_user_id = None

        if not current_user:
            return jsonify({"error": "User not found"}), 404

        # Check if email or username already exists (if being updated)
        if 'email' in update_fields:
            existing_user = mongo.db.users.find_one({
                "email": update_fields['email'],
                "_id": {"$ne": current_user_id}
            })
            if existing_user:
                return jsonify({"error": "Email already exists"}), 400

        if 'username' in update_fields:
            existing_user = mongo.db.users.find_one({
                "username": update_fields['username'],
                "_id": {"$ne": current_user_id}
            })
            if existing_user:
                return jsonify({"error": "Username already exists"}), 400

        # Update user profile
        result = mongo.db.users.update_one(
            {"_id": current_user_id},
            {"$set": update_fields}
        )

        if result.matched_count == 0:
            return jsonify({"error": "User not found"}), 404

        # Get updated user data
        updated_user = mongo.db.users.find_one({"_id": current_user_id}, {"password": 0})
        if updated_user:
            updated_user["_id"] = str(updated_user["_id"])

        return jsonify({
            "message": "Profile updated successfully",
            "user": updated_user
        }), 200

    except Exception as e:
        print(f"❌ Error updating profile: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Failed to update profile: {str(e)}"}), 500

# Get current user profile
@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get current user profile information"""
    from flask_jwt_extended import get_jwt_identity, get_jwt
    from bson import ObjectId

    try:
        user_id = get_jwt_identity()
        claims = get_jwt()
        print(f"🔍 Getting profile for user: {user_id}")
        print(f"🔍 JWT claims: {claims}")

        # The user_id from get_jwt_identity() is already the ObjectId string
        # Try to use it directly as ObjectId
        try:
            print(f"🔍 Using user ID from JWT identity: {user_id}")
            user = mongo.db.users.find_one({"_id": ObjectId(user_id)}, {"password": 0})
        except Exception as e:
            print(f"🔍 Failed to use as ObjectId, trying username search: {e}")
            # Fallback to username search
            user = mongo.db.users.find_one({"username": user_id}, {"password": 0})

        if not user:
            print(f"❌ User not found for ID/username: {user_id}")
            return jsonify({"error": "User not found"}), 404

        user["_id"] = str(user["_id"])
        print(f"🔍 Found user profile: {user}")
        print(f"🔍 User fields: first_name={user.get('first_name')}, last_name={user.get('last_name')}")
        return jsonify(user), 200

    except Exception as e:
        print(f"❌ Error getting profile: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Failed to get profile: {str(e)}"}), 500

# Avatar upload endpoint
@auth_bp.route('/avatar', methods=['POST'])
@jwt_required()
def upload_avatar():
    """Upload user avatar"""
    from flask_jwt_extended import get_jwt_identity
    from bson import ObjectId

    try:
        user_id = get_jwt_identity()

        if 'avatar' not in request.files:
            return jsonify({"error": "No avatar file provided"}), 400

        file = request.files['avatar']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Check file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({"error": "Invalid file type. Only PNG, JPG, JPEG, and GIF are allowed"}), 400

        # Check file size (max 5MB)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        if file_size > 5 * 1024 * 1024:  # 5MB
            return jsonify({"error": "File too large. Maximum size is 5MB"}), 400

        # Create uploads directory if it doesn't exist
        upload_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'uploads', 'avatars')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{user_id}_{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(upload_dir, filename)

        # Save file
        file.save(file_path)

        # Update user avatar in database
        avatar_url = f"/auth/avatar/{filename}"
        try:
            # Try to use ObjectId first
            result = mongo.db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {"avatar": avatar_url}}
            )
        except Exception:
            # Fallback to username search
            result = mongo.db.users.update_one(
                {"username": user_id},
                {"$set": {"avatar": avatar_url}}
            )

        if result.matched_count == 0:
            # Clean up uploaded file if user not found
            os.remove(file_path)
            return jsonify({"error": "User not found"}), 404

        return jsonify({
            "message": "Avatar uploaded successfully",
            "avatar_url": avatar_url
        }), 200

    except Exception as e:
        return jsonify({"error": f"Failed to upload avatar: {str(e)}"}), 500

# Serve avatar files
@auth_bp.route('/avatar/<filename>')
def get_avatar(filename):
    """Serve avatar files"""
    try:
        upload_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'uploads', 'avatars')
        return send_from_directory(upload_dir, filename)
    except Exception as e:
        return jsonify({"error": "Avatar not found"}), 404

# Session verification endpoint
@auth_bp.route('/verify-session', methods=['GET'])
@jwt_required()
def verify_session():
    """Verify if the current session is still valid and user is not banned"""
    from flask_jwt_extended import get_jwt
    from app.extensions import mongo
    from app.models.session import SessionManager

    try:
        claims = get_jwt()
        session_id = claims.get("session_id")

        if session_id:
            # Check if session is still active in database
            session = mongo.db.user_sessions.find_one({"session_id": session_id})
            if not session or not session.get("is_active", False):
                return jsonify({"msg": "Session has been terminated", "code": "SESSION_TERMINATED"}), 401

            # Check if user still exists and is not banned
            user = mongo.db.users.find_one({"_id": session.get("user_id")})
            if not user:
                # User has been deleted, terminate the session
                SessionManager.end_session(session_id)
                return jsonify({"msg": "Account has been deleted", "code": "ACCOUNT_DELETED"}), 403

            if user.get("banned", False):
                # User is banned, terminate the session
                SessionManager.end_session(session_id)
                return jsonify({"msg": "Account has been banned", "code": "ACCOUNT_BANNED"}), 403

        return jsonify({"msg": "Session is valid"}), 200

    except Exception as e:
        return jsonify({"msg": "Session verification failed", "error": str(e)}), 500
auth_bp.route('/forgot-password', methods=['POST'])(send_reset_link)
auth_bp.route('/reset-password', methods=['POST'])(reset_password)

# Debug route to check user status
@auth_bp.route('/debug/user-status', methods=['GET'])
def debug_user_status():
    email = request.args.get('email')
    if not email:
        return jsonify({"error": "Email parameter required"}), 400

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"error": "User not found"}), 404

    return jsonify({
        "email": user["email"],
        "email_verified": user.get("email_verified", False),
        "username": user.get("username", ""),
        "created_at": str(user.get("_id").generation_time) if user.get("_id") else None
    }), 200

@auth_bp.route('/notification-preferences', methods=['GET', 'POST', 'OPTIONS'])
@handle_options
@jwt_required()
def notification_preferences():
    """
    GET: Récupère les préférences de notifications de l'utilisateur
    POST: Met à jour les préférences de notifications de l'utilisateur
    """
    try:
        user_id = get_jwt_identity()
        if not user_id:
            return jsonify({"error": "User not authenticated"}), 401

        if request.method == 'GET':
            # Récupérer les préférences de notifications
            try:
                current_user_id = ObjectId(user_id)
                user = mongo.db.users.find_one({"_id": current_user_id})
            except Exception:
                user = mongo.db.users.find_one({"username": user_id})

            if not user:
                return jsonify({"error": "User not found"}), 404

            # Préférences par défaut
            default_preferences = {
                'security_alerts': True,
                'system_updates': True,
                'analysis_completed': True,
                'login_alerts': False,
                'profile_changes': True,
                'email_notifications': True,
                'toast_notifications': True,
            }

            # Récupérer les préférences existantes ou utiliser les valeurs par défaut
            preferences = user.get('notification_preferences', default_preferences)

            return jsonify(preferences), 200

        elif request.method == 'POST':
            # Mettre à jour les préférences de notifications
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Valider les champs de préférences
            valid_fields = [
                'security_alerts', 'system_updates', 'analysis_completed',
                'login_alerts', 'profile_changes', 'email_notifications',
                'toast_notifications'
            ]

            preferences = {}
            for field in valid_fields:
                if field in data:
                    if isinstance(data[field], bool):
                        preferences[field] = data[field]
                    else:
                        return jsonify({"error": f"Invalid value for {field}, must be boolean"}), 400

            if not preferences:
                return jsonify({"error": "No valid preferences provided"}), 400

            # Mettre à jour l'utilisateur
            try:
                current_user_id = ObjectId(user_id)
                result = mongo.db.users.update_one(
                    {"_id": current_user_id},
                    {"$set": {"notification_preferences": preferences}}
                )
            except Exception:
                result = mongo.db.users.update_one(
                    {"username": user_id},
                    {"$set": {"notification_preferences": preferences}}
                )

            if result.modified_count > 0:
                return jsonify({
                    "message": "Notification preferences updated successfully",
                    "preferences": preferences
                }), 200
            else:
                return jsonify({"error": "Failed to update preferences"}), 500

    except Exception as e:
        print(f"❌ Error in notification preferences: {e}")
        return jsonify({"error": "Internal server error"}), 500

@auth_bp.route('/notifications', methods=['GET', 'POST', 'OPTIONS'])
@handle_options
@jwt_required()
def notifications():
    """
    GET: Récupère les notifications de l'utilisateur
    POST: Crée une nouvelle notification
    """
    try:
        user_id = get_jwt_identity()
        if not user_id:
            return jsonify({"error": "User not authenticated"}), 401

        if request.method == 'GET':
            # Paramètres de requête
            limit = int(request.args.get('limit', 50))
            unread_only = request.args.get('unread_only', 'false').lower() == 'true'

            # Récupérer les notifications
            notifications = NotificationService.get_user_notifications(
                user_id=user_id,
                limit=limit,
                unread_only=unread_only
            )

            return jsonify({
                "notifications": notifications,
                "total": len(notifications)
            }), 200

        elif request.method == 'POST':
            # Créer une nouvelle notification
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            notification_type = data.get('type')
            title = data.get('title')
            message = data.get('message')
            notification_data = data.get('data', {})

            if not all([notification_type, title, message]):
                return jsonify({"error": "Missing required fields: type, title, message"}), 400

            # Stocker la notification
            notification_id = NotificationService.store_notification(
                user_id=user_id,
                notification_type=notification_type,
                title=title,
                message=message,
                data=notification_data
            )

            if notification_id:
                return jsonify({
                    "message": "Notification created successfully",
                    "notification_id": notification_id
                }), 201
            else:
                return jsonify({"error": "Failed to create notification"}), 500

    except Exception as e:
        print(f"❌ Error in notifications endpoint: {e}")
        return jsonify({"error": "Internal server error"}), 500

@auth_bp.route('/notifications/<notification_id>/read', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def mark_notification_read(notification_id):
    """
    Marque une notification comme lue
    """
    try:
        success = NotificationService.mark_notification_read(notification_id)

        if success:
            return jsonify({"message": "Notification marked as read"}), 200
        else:
            return jsonify({"error": "Failed to mark notification as read"}), 400

    except Exception as e:
        print(f"❌ Error marking notification as read: {e}")
        return jsonify({"error": "Internal server error"}), 500

@auth_bp.route('/notifications/mark-all-read', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def mark_all_notifications_read():
    """
    Marque toutes les notifications comme lues
    """
    try:
        user_id = get_jwt_identity()
        if not user_id:
            return jsonify({"error": "User not authenticated"}), 401

        count = NotificationService.mark_all_read(user_id)

        return jsonify({
            "message": f"{count} notifications marked as read",
            "count": count
        }), 200

    except Exception as e:
        print(f"❌ Error marking all notifications as read: {e}")
        return jsonify({"error": "Internal server error"}), 500
